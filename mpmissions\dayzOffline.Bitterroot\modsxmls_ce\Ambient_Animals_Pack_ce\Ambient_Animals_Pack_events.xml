<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<events>
	<!--Ambient_Animals_Pack-->	
    <event name="AnimalSnake">
        <nominal>5</nominal>
        <min>4</min>
        <max>6</max>
        <lifetime>180</lifetime>
        <restock>0</restock>
        <saferadius>200</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="1"/>
        <position>fixed</position>
        <limit>child</limit>
        <active>1</active>
		<children>
		<child lootmax="0" lootmin="0" max="4" min="2" type="Animal_Snake"/>
		</children>
    </event>		
    <event name="AmbientOtters">
        <nominal>3</nominal>
        <min>0</min>
        <max>20</max>
        <lifetime>33</lifetime>
        <restock>15</restock>
        <saferadius>40</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="0"/>
        <position>fixed</position>
        <limit>mixed</limit>
        <active>1</active>
        <children>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Otter"/>
        </children>
    </event>
	<event name="AmbientRabbits">
        <nominal>3</nominal>
        <min>0</min>
        <max>50</max>
        <lifetime>33</lifetime>
        <restock>15</restock>
        <saferadius>40</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="0"/>
        <position>fixed</position>
        <limit>mixed</limit>
        <active>1</active>
        <children>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Rabbit_Brown"/>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Rabbit_Grey"/>
        </children>
    </event>		
    <event name="AmbientRats">
        <nominal>3</nominal>
        <min>0</min>
        <max>50</max>
        <lifetime>33</lifetime>
        <restock>15</restock>
        <saferadius>40</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="0"/>
        <position>fixed</position>
        <limit>mixed</limit>
        <active>1</active>
        <children>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Rat_Grey"/>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Rat_White"/>
        </children>
    </event>		
    <event name="AmbientRavens">
        <nominal>3</nominal>
        <min>0</min>
        <max>50</max>
        <lifetime>33</lifetime>
        <restock>15</restock>
        <saferadius>40</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="0"/>
        <position>fixed</position>
        <limit>mixed</limit>
        <active>1</active>
        <children>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Raven"/>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Raven2"/>
        </children>
    </event>		
    <event name="AmbientSeagulls">
        <nominal>3</nominal>
        <min>0</min>
        <max>50</max>
        <lifetime>33</lifetime>
        <restock>15</restock>
        <saferadius>40</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="0"/>
        <position>fixed</position>
        <limit>mixed</limit>
        <active>1</active>
        <children>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Seagull"/>
        </children>
    </event>		
    <event name="AmbientSquirrels">
        <nominal>3</nominal>
        <min>0</min>
        <max>50</max>
        <lifetime>33</lifetime>
        <restock>15</restock>
        <saferadius>40</saferadius>
        <distanceradius>0</distanceradius>
        <cleanupradius>0</cleanupradius>
        <flags deletable="0" init_random="0" remove_damaged="0"/>
        <position>fixed</position>
        <limit>mixed</limit>
        <active>1</active>
        <children>
            <child lootmax="0" lootmin="0" max="3" min="1" type="Animal_Squirrel"/>
        </children>
    </event>						
</events>