<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<economycore>
	<classes>
<!--
These are rootclasses to be used by economy.
Do not forget to add attribute act="character", if root class represents character (player, infected, animal)
Do not forget to add attribute act="car", if root class represents moveable vehicles
-->
		<rootclass name="DefaultWeapon" /> <!-- weapons -->
		<rootclass name="DefaultMagazine" /> <!-- magazines -->
		<rootclass name="Inventory_Base" /> <!-- inventory items -->
		<rootclass name="HouseNoDestruct" reportMemoryLOD="no" /> <!-- houses, wrecks -->
		<rootclass name="SurvivorBase" act="character" reportMemoryLOD="no" /> <!-- player characters -->
		<rootclass name="DZ_LightAI" act="character" reportMemoryLOD="no" /> <!-- infected, animals -->
		<rootclass name="CarScript" act="car" reportMemoryLOD="no" /> <!-- cars (sedan, hatchback, transitBus, V3S, ...) -->
		<rootclass name="BoatScript" act="car" reportMemoryLOD="no" /> <!-- boats -->
	</classes>
	<defaults>
		<default name="dyn_radius" value="30" />
		<default name="dyn_smin" value="0" />
		<default name="dyn_smax" value="0" />
		<default name="dyn_dmin" value="1" />
		<default name="dyn_dmax" value="5" />
		<default name="log_ce_loop" value="false"/>
		<default name="log_ce_dynamicevent" value="false"/>
		<default name="log_ce_vehicle" value="false"/>
		<default name="log_ce_lootspawn" value="false"/>
		<default name="log_ce_lootcleanup" value="false"/>
		<default name="log_ce_lootrespawn" value="false"/>
		<default name="log_ce_statistics" value="false"/>
		<default name="log_ce_zombie" value="false"/>
		<default name="log_storageinfo" value="false"/>
		<default name="log_hivewarning" value="true"/>
		<default name="log_missionfilewarning" value="true"/>
		<default name="save_events_startup" value="true"/>
		<default name="save_types_startup" value="true"/>
	</defaults>


	<!--AdvancedBanking_V2-->
	<ce folder="modsxmls_ce\AdvancedBanking_V2_ce">
		<file name="AdvancedBanking_V2_types.xml" type="types" />
	</ce>
	<!--AirRaid-->
	<ce folder="modsxmls_ce\AirRaid_ce">
		<file name="AirRaid_types.xml" type="types" />
	</ce>
	<!--Ambient_Animals_Pack-->
	<ce folder="modsxmls_ce\Ambient_Animals_Pack_ce">
		<file name="Ambient_Animals_Pack_types.xml" type="types" />
		<file name="Ambient_Animals_Pack_spawnabletypes.xml" type="spawnabletypes" />
		<file name="Ambient_Animals_Pack_events.xml" type="events" />
    </ce>
	<!--BaseBuildingPlus-->
	<ce folder="modsxmls_ce\BaseBuildingPlus_ce">
		<file name="BaseBuildingPlus_types.xml" type="types" />
		<file name="BaseBuildingPlus_spawnabletypes.xml" type="spawnabletypes" />
	</ce>		
	<!--BBP_Nouveau-->
	<ce folder="modsxmls_ce\BBP_Nouveau_ce">
		<file name="BBP_Nouveau_types.xml" type="types" />
	</ce>		
	<!--BBPItemPack-->
	<ce folder="modsxmls_ce\BBPItemPack_ce">
		<file name="BBPItemPack_types.xml" type="types" />
		<file name="BBPItemPack_spawnabletypes.xml" type="spawnabletypes" />
	</ce>		
	<!--Bitterroot-->
	<ce folder="modsxmls_ce\Bitterroot_ce">
		<file name="Bitterroot_types.xml" type="types" />
		<file name="Bitterroot_spawnabletypes.xml" type="spawnabletypes" />
		<file name="Bitterroot_events.xml" type="events" />
	</ce>
	<!--BuildingsModPack7-->
	<ce folder="modsxmls_ce\BuildingsModPack7_ce">
		<file name="BuildingsModPack7_types.xml" type="types" />
	<!--
	</ce>
	CarBuildingPlus
	<ce folder="modsxmls_ce\CarBuildingPlus_ce">
		<file name="CarBuildingPlus_types.xml" type="types" />
		<file name="CarBuildingPlus_spawnabletypes.xml" type="spawnabletypes" />
		<file name="CarBuildingPlus_events.xml" type="events" />
	-->
	</ce>
	<!--CnG_UAZ_452-->
	<ce folder="modsxmls_ce\CnG_UAZ_452_ce">
		<file name="CnG_UAZ_452_types.xml" type="types" />
		<file name="CnG_UAZ_452_spawnabletypes.xml" type="spawnabletypes" />
		<file name="CnG_UAZ_452_events.xml" type="events" />
	</ce>
	<!--DHGS_Hunting-->
	<ce folder="modsxmls_ce\DHGS_Hunting_ce">
		<file name="DHGS_Hunting_types.xml" type="types" />
		<file name="DHGS_Hunting_spawnabletypes.xml" type="spawnabletypes" />
		<file name="DHGS_Hunting_events.xml" type="events" />
	</ce>
	<!--Expansion-->
	<ce folder="modsxmls_ce\expansion_ce">
		<file name="expansion_types.xml" type="types" />
		<file name="expansion_spawnabletypes.xml" type="spawnabletypes" />
		<file name="expansion_events.xml" type="events" />
	</ce>
	<!--Fortune_Cars-->
	<ce folder="modsxmls_ce\Fortune_Cars_ce">
		<file name="Fortune_Cars_types.xml" type="types" />
		<file name="Fortune_Cars_spawnabletypes.xml" type="spawnabletypes" />
		<file name="Fortune_Cars_events.xml" type="events" />
	</ce>	
	<!--GoreZ-->
	<ce folder="modsxmls_ce\GoreZ_ce">
		<file name="GoreZ_types.xml" type="types" />
	</ce>
	<!--IRP_Land_Rover_Defender_110-->
	<ce folder="modsxmls_ce\IRP_Land_Rover_Defender_110_ce">
		<file name="IRP_Land_Rover_Defender_110_types.xml" type="types" />
		<file name="IRP_Land_Rover_Defender_110_spawnabletypes.xml" type="spawnabletypes" />
		<file name="IRP_Land_Rover_Defender_110_events.xml" type="events" />
	</ce>
	<!--Mortys_Weapons-->
	<ce folder="modsxmls_ce\Mortys_Weapons_ce">
		<file name="Mortys_Weapons_types.xml" type="types" />
		<file name="Mortys_Weapons_spawnabletypes.xml" type="spawnabletypes" />
	</ce>
	<!--MuchDecos-->
	<ce folder="modsxmls_ce\MuchDecos_ce">
		<file name="MuchDecos_types.xml" type="types" />
		<file name="MuchDecos_spawnabletypes.xml" type="spawnabletypes" />
	</ce>		
	<!--OV_AllinOne-->
	<ce folder="modsxmls_ce\OV_AllinOne_ce">
		<file name="OV_AllinOne_types.xml" type="types" />
	</ce>
	<!--Paragon_Arsenal-->
	<ce folder="modsxmls_ce\Paragon_Arsenal_ce">
		<file name="Paragon_Arsenal_types.xml" type="types" />
		<file name="Paragon_Arsenal_spawnabletypes.xml" type="spawnabletypes" />
	</ce>
	<!--Paragon_Gear_and_Armor-->
	<ce folder="modsxmls_ce\Paragon_Gear_and_Armor_ce">
		<file name="Paragon_Gear_and_Armor_types.xml" type="types" />
		<file name="Paragon_Gear_and_Armor_spawnabletypes.xml" type="spawnabletypes" />
	</ce>
	<!--Paragon_Optics-->
	<ce folder="modsxmls_ce\Paragon_Optics_ce">
		<file name="Paragon_Optics_types.xml" type="types" />
		<file name="Paragon_Optics_spawnabletypes.xml" type="spawnabletypes" />
	</ce>	
	<!--PvZmoD_TheDarkHorde-->
	<ce folder="modsxmls_ce\PvZmoD_TheDarkHorde_ce">
		<file name="PvZmoD_TheDarkHorde_types.xml" type="types" />
		<file name="PvZmoD_TheDarkHorde_spawnabletypes.xml" type="spawnabletypes" />
	</ce>
	<!--RaG_BaseItems-->
	<ce folder="modsxmls_ce\RaG_BaseItems_ce">
		<file name="RaG_BaseItems_types.xml" type="types" />
		<file name="RaG_BaseItems_spawnabletypes.xml" type="spawnabletypes" />
	</ce>		
	<!--RaG_Vehicle_Pack-->
	<ce folder="modsxmls_ce\RaG_Vehicle_Pack_ce">
		<file name="RaG_Vehicle_Pack_types.xml" type="types" />
		<file name="RaG_Vehicle_Pack_spawnabletypes.xml" type="spawnabletypes" />
		<file name="RaG_Vehicle_Pack_events.xml" type="events" />
	</ce>		
	<!--RedFalcon_Flight_System_Heliz-->
	<ce folder="modsxmls_ce\RedFalcon_Flight_System_Heliz_ce">
		<file name="RedFalcon_Flight_System_Heliz_types.xml" type="types" />
		<file name="RedFalcon_Flight_System_Heliz_spawnabletypes.xml" type="spawnabletypes" />
		<file name="RedFalcon_Flight_System_Heliz_events.xml" type="events" />
	</ce>		
	<!--RenegadeGear-->
	<ce folder="modsxmls_ce\RenegadeGear_ce">
		<file name="RenegadeGear_types.xml" type="types" />
		<file name="RenegadeGear_spawnabletypes.xml" type="spawnabletypes" />
	</ce>		
	<!--RenegadeGearExtension-->
	<ce folder="modsxmls_ce\RenegadeGearExtension_ce">
		<file name="RenegadeGearExtension_types.xml" type="types" />
		<file name="RenegadeGearExtension_spawnabletypes.xml" type="spawnabletypes" />
	</ce>		
	<!--RUSForma_vehicles-->
	<ce folder="modsxmls_ce\RUSForma_vehicles_ce">
		<file name="RUSForma_vehicles_types.xml" type="types" />
		<file name="RUSForma_vehicles_spawnabletypes.xml" type="spawnabletypes" />
		<file name="RUSForma_vehicles_events.xml" type="events" />
	</ce>
	<!--SNAFU_Weapons-->
	<ce folder="modsxmls_ce\SNAFU_Weapons_ce">
		<file name="SNAFU_Weapons_types.xml" type="types" />
		<file name="SNAFU_Weapons_spawnabletypes.xml" type="spawnabletypes" />
	</ce>
	<!--SPBuilding-->
	<ce folder="modsxmls_ce\SPBuilding_ce">
		<file name="SPBuilding_types.xml" type="types" />
	</ce>
</economycore>