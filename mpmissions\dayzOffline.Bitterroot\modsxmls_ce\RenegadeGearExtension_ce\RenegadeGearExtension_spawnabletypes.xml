<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>

<spawnabletypes>
	<type name="Renegade_AdminArmband">

	</type>
	<type name="Renegade_AdminProtectorCase">

	</type>
	<type name="Renegade_AdminPlateCarrierPouches">

	</type>
	<type name="Renegade_AdminFirstAidKit">

	</type>
	<type name="Renegade_Armband_Black">

	</type>
	<type name="Renegade_Armband_BlackBlueDark">

	</type>
	<type name="Renegade_Armband_BlackCamo">

	</type>
	<type name="Renegade_Armband_BlackGreen">

	</type>
	<type name="Renegade_Armband_BlackOrange">

	</type>
	<type name="Renegade_Armband_BlackPixel">

	</type>
	<type name="Renegade_Armband_BlackScale">

	</type>
	<type name="Renegade_Armband_BlackYellowPixel">

	</type>
	<type name="Renegade_Armband_BlkBlueCamo">

	</type>
	<type name="Renegade_Armband_BlueCamo">

	</type>
	<type name="Renegade_Armband_BlueCamoPixel">

	</type>
	<type name="Renegade_Armband_BlueGrunge_2">

	</type>
	<type name="Renegade_Armband_BlueGrunge">

	</type>
	<type name="Renegade_Armband_BluePixel">


	</type>
	<type name="Renegade_Armband_BrightGreen">

	</type>
	<type name="Renegade_Armband_Camo1">

	</type>
	<type name="Renegade_Armband_CamoScale">

	</type>
	<type name="Renegade_Armband_DarkCamo">

	</type>
	<type name="Renegade_Armband_Forest">

	</type>
	<type name="Renegade_Armband_ForestGreen">

	</type>
	<type name="Renegade_Armband_Galaxy">

	</type>
	<type name="Renegade_Armband_GreenCamo_2">

	</type>
	<type name="Renegade_Armband_GreenCamo_3">

	</type>
	<type name="Renegade_Armband_GreenCamo_4">

	</type>
	<type name="Renegade_Armband_GreenCamo">

	</type>
	<type name="Renegade_Armband_GreenPixel">

	</type>
	<type name="Renegade_Armband_LightCamo">

	</type>
	<type name="Renegade_Armband_MilitaryCamo_2">

	</type>
	<type name="Renegade_Armband_MilitaryCamo">

	</type>
	<type name="Renegade_Armband_NavyGold">

	</type>
	<type name="Renegade_Armband_OrangeCamo">

	</type>
	<type name="Renegade_Armband_OrangeLines">

	</type>
	<type name="Renegade_Armband_PinkCamo">

	</type>
	<type name="Renegade_Armband_PureBlack">

	</type>
	<type name="Renegade_Armband_RedCamo">

	</type>
	<type name="Renegade_Armband_RedGreyCamo">

	</type>
	<type name="Renegade_Armband_RedScale">

	</type>
	<type name="Renegade_Armband_WhiteCamo">

	</type>
	<type name="Renegade_Canteen_Colorbase">

	</type>
	<type name="Renegade_NBC_Bag">

	</type>
	<type name="Renegade_RavenclawMediumTent">

	</type>
	<type name="Renegade_SlytherinMediumTent">

	</type>
	<type name="Renegade_CryffindorMediumTent">

	</type>
	<type name="Renegade_GryffindorLargeTent">

	</type>
	<type name="Renegade_RavenclawLargeTent">

	</type>
	<type name="Renegade_SlytherinLargeTent">

	</type>
	<type name="Renegade_SlytherinCarTent">

	</type>
	<type name="Renegade_RavenclawCarTent">

	</type>
	<type name="Renegade_CryffindorCarTent">

	</type>
	<type name="Renegade_FAL_MilitaryCamo_Buttstock">

	</type>
	<type name="Renegade_RavenclawLargeTent">

	</type>
	<type name="Renegade_SlytherinLargeTent">

	</type>
	<type name="Renegade_SlytherinCarTent">

	</type>
	<type name="Renegade_RavenclawCarTent">

	</type>
	<type name="Renegade_GryffindorCarTent">

	</type>
	<type name="Renegade_FAL_MilitaryCamo_Buttstock">

	</type>
	<type name="Renegade_FAL_Camo_Buttstock">

	</type>
	<type name="Renegade_MilitaryCamo_HuntingOptic">

	</type>
	<type name="Renegade_Camo_HuntingOptic">

	</type>
	<type name="Renegade_MilitaryCamo_RifleSuppresso">

	</type>
	<type name="Renegade_Camo_RifleSuppressor">

	</type>
	<type name="Renegade_AdminJacket">

	</type>
	<type name="Renegade_AdminCargoPants">

	</type>
	<type name="Renegade_AdminHelmet">

	</type>
	<type name="Renegade_AdminPlateCarrier">

	</type>
	<type name="Flashlight">

	</type>
	<type name="Renegade_1000_TortillaBag">

	</type>
	<type name="RenegadeAdmin_BlackTortillaBag">

	</type>
	<type name="Renegade_HuntingBag_Colorbase">

	</type>
	<type name="Renegade_TortillaBag">

	</type>
	<type name="Renegade_MilitaryCamo_Deagle">

	</type>
	<type name="Renegade_FAL_MilitaryCamo">

	</type>
	<type name="Renegade_FAL Camo">
	</type>
</spawnabletypes>